#!/usr/bin/env ruby
# frozen_string_literal: true

# Simple test script to verify Sentry logging memory profiling works

puts "Testing Sentry logging memory profiling..."

begin
  require 'memory_profiler'
  puts "✓ memory_profiler loaded"
rescue LoadError => e
  puts "✗ memory_profiler not available: #{e.message}"
  puts "Installing memory_profiler..."
  system("gem install memory_profiler")
  require 'memory_profiler'
  puts "✓ memory_profiler installed and loaded"
end

begin
  require "sentry-ruby"
  puts "✓ sentry-ruby loaded"
rescue LoadError => e
  puts "✗ sentry-ruby not available: #{e.message}"
  exit 1
end

begin
  require "sentry/benchmarks/benchmark_transport"
  puts "✓ benchmark_transport loaded"
rescue LoadError => e
  puts "✗ benchmark_transport not available: #{e.message}"
  puts "Creating benchmark transport..."
  
  # Create the benchmark transport inline if it doesn't exist
  module Sentry
    class BenchmarkTransport < Transport
      attr_accessor :events, :envelopes, :log_events

      def initialize(*)
        super
        @events = []
        @envelopes = []
        @log_events = []
      end

      def send_event(event)
        @events << event
        event
      end

      def send_envelope(envelope)
        @envelopes << envelope
        envelope
      end

      def send_data(data, options = {})
        true
      end
    end
  end
  puts "✓ benchmark_transport created inline"
end

# Configure Sentry
puts "\nConfiguring Sentry..."
Sentry.init do |config|
  config.dsn = "dummy://12345:<EMAIL>:3000/sentry/42"
  config.transport.transport_class = Sentry::BenchmarkTransport
  config.enable_logs = true
  config.sdk_logger = ::Logger.new(nil)
  config.breadcrumbs_logger = []
  config.send_client_reports = false
  config.auto_session_tracking = false
end

puts "✓ Sentry configured"
puts "✓ Logging enabled: #{Sentry.configuration.enable_logs}"

# Test basic logging
puts "\nTesting basic logging..."
if Sentry.logger
  puts "✓ Sentry.logger available"
  
  # Test memory profiling
  puts "\nRunning memory profile test..."
  report = MemoryProfiler.report do
    10.times do |i|
      Sentry.logger.info("Test message #{i}", iteration: i, timestamp: Time.now.to_f)
    end
  end
  
  puts "✓ Memory profiling completed"
  puts "  Total allocated objects: #{report.total_allocated}"
  puts "  Total retained objects: #{report.total_retained}"
  puts "  Total allocated memory: #{report.total_allocated_memsize} bytes"
  puts "  Total retained memory: #{report.total_retained_memsize} bytes"
  
  # Check transport
  transport = Sentry.get_current_client.transport
  puts "  Transport events: #{transport.events.size}"
  puts "  Transport envelopes: #{transport.envelopes.size}"
  
  puts "\n✅ All tests passed! The logging memory profiler should work."
else
  puts "✗ Sentry.logger not available"
  exit 1
end
